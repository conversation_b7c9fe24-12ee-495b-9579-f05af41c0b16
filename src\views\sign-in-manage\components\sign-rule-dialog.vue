<template>
  <common-dialog
    title="签到奖励配置"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
    :show-footer="true"
    @closed="handleClosed"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="dialog-main">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-position="top">
        <el-form-item label="签到规则配置" prop="ruleContent">
          <wang-editor ref="ruleContent" v-model="form.ruleContent" :content.sync="form.ruleContent" :height="300" />
        </el-form-item>
        <el-form-item label="签到提醒文案" prop="remindText">
          <el-input v-model="form.remindText" type="textarea" :maxlength="20" show-word-limit placeholder="请输入签到提醒文案" />
        </el-form-item>
        <el-form-item label="签到奖励配置（铁粉用户享奖励翻倍权益）">
          <el-table :data="form.rewardList" border size="small" class="table-container">
            <el-table-column label="连续签到" width="120" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.days }}天</span>
              </template>
            </el-table-column>
            <el-table-column label="奖励智米数" align="center">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'rewardList.' + scope.$index + '.points'"
                  :rules="rules.points"
                  :show-message="false"
                  class="table-form-item"
                >
                  <el-input-number v-model="scope.row.points" :min="0" :max="999" size="mini" controls-position="right" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="rewardListError" class="el-form-item__error" style="position: relative;">
            {{ rewardListError }}
          </div>
        </el-form-item>
        <el-form-item label="变更记录">
          <el-table :data="paginatedRecords" border size="small" class="table-container">
            <el-table-column prop="editor" label="编辑人" align="center" />
            <el-table-column prop="content" label="编辑内容" align="center" />
            <el-table-column prop="editTime" label="编辑时间" width="180" align="center" />
          </el-table>
          <div class="yz-table-pagination">
            <pagination
              v-show="total > 0"
              size="mini"
              :total="total"
              :page.sync="listQuery.page"
              :limit.sync="listQuery.limit"
              @pagination="getList"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import WangEditor from '@/components/WangEditor';
import Pagination from '@/components/Pagination';
import { getSignInRules, updateSignInRules } from '@/api/sign-in';

export default {
  name: 'SignRuleDialog',

  components: {
    WangEditor,
    Pagination
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      // 模拟数据
      mockData: {
        ruleContent: '<p>1. 每日签到可获得智米奖励</p><p>2. 连续签到可获得额外奖励</p><p>3. 中断签到将重新计算连续天数</p>',
        remindText: '记得每天来签到领智米哦~',
        rewardList: Array.from({ length: 7 }, (_, i) => ({
          days: i + 1,
          points: undefined
        })),
        changeRecords: [
          {
            editor: '管理员',
            content: '修改了签到规则说明',
            editTime: '2024-03-20 10:00:00'
          },
          {
            editor: '管理员',
            content: '调整了连续签到奖励',
            editTime: '2024-03-19 15:30:00'
          }
        ]
      },
      form: {
        ruleContent: '',
        remindText: '',
        rewardList: []
      },
      rules: {
        ruleContent: [
          { required: true, message: '请输入签到规则内容', trigger: 'blur' }
        ],
        remindText: [
          { required: true, message: '请输入签到提醒文案', trigger: 'blur' },
          { max: 20, message: '提醒文案不能超过20个字符', trigger: 'blur' }
        ],
        points: [
          { required: true, message: '请填写奖励智米数', trigger: 'blur' }
        ]
      },
      rewardListError: null,
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      changeRecords: []
    };
  },

  computed: {
    paginatedRecords() {
      const start = (this.listQuery.page - 1) * this.listQuery.limit;
      const end = start + this.listQuery.limit;
      return this.changeRecords.slice(start, end);
    }
  },

  watch: {
    visible(val) {
      if (val) {
        this.initData();
      }
    },
    'form.rewardList': {
      deep: true,
      handler(newVal) {
        if (this.rewardListError) {
          const isStillInvalid = newVal.some(item => item.points === undefined || item.points === null);
          if (!isStillInvalid) {
            this.rewardListError = null;
          }
        }
      }
    }
  },

  methods: {
    // 初始化数据
    async initData() {
      try {
        const res = await getSignInRules();
        if (res.code === 200) {
          this.form = {
            ruleContent: res.data.ruleContent,
            remindText: res.data.remindText,
            rewardList: res.data.rewardList.length > 0 ? res.data.rewardList : Array.from({ length: 7 }, (_, i) => ({
              days: i + 1,
              points: undefined
            }))
          };
          this.$refs.ruleContent.setContent(res.data.ruleContent);
          this.changeRecords = res.data.changeRecords || [];
          this.total = this.changeRecords.length;
        } else {
          throw new Error('获取数据失败');
        }
      } catch (error) {
        console.error(error);
        this.$message.error('获取数据失败');
        // 使用默认数据
        this.form = {
          ruleContent: this.mockData.ruleContent,
          remindText: this.mockData.remindText,
          rewardList: [...this.mockData.rewardList]
        };
        this.$refs.ruleContent.setContent(this.mockData.ruleContent);
        this.changeRecords = [...this.mockData.changeRecords];
        this.total = this.changeRecords.length;
      }
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.$emit('update:visible', false);
    },

    handleConfirm() {
      this.rewardListError = null;
      this.$refs.ruleForm.validate((valid, invalidFields) => {
        if (valid) {
          this.handleSaveSignRule(this.form);
        } else {
          if (invalidFields && Object.keys(invalidFields).some(key => key.startsWith('rewardList.'))) {
            this.rewardListError = '请填写所有天数的奖励智米数';
          }
        }
      });
    },

    handleClosed() {
      this.$refs.ruleForm.resetFields();
      this.form = {
        ruleContent: '',
        remindText: '',
        rewardList: []
      };
      this.changeRecords = [];
      this.listQuery.page = 1;
      this.total = 0;
    },

    // 保存签到规则
    async handleSaveSignRule(data) {
      try {
        await updateSignInRules(data);
        this.$message.success('保存成功');
        this.$emit('update:visible', false);
      } catch (error) {
        console.error(error);
        this.$message.error('保存失败');
      }
    },

    getList() {
      // 更新分页数据
      this.total = this.changeRecords.length;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number {
  width: 100%;
}

.yz-table-pagination {
  text-align: right;
}

.table-form-item {
  margin-bottom: 0;
}
</style>

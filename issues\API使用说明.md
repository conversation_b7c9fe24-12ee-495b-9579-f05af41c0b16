# 签到管理API使用说明

## 概述
已将`src/api/sign-in.js`中的所有函数修改为返回配置对象，页面需要使用`this.$post`来调用接口。

## API函数返回格式

每个API函数现在返回一个包含以下属性的对象：
- `url`: 接口地址
- `data`: 请求参数
- `mapResponse`: 数据映射函数（可选）

对于需要调用多个接口的函数（如签到规则配置），会返回多个URL和数据。

## 使用示例

### 1. 获取签到列表

```javascript
// 在页面中使用
async fetchTableData() {
  try {
    this.tableLoading = true;
    const params = {
      page: this.page.current,
      size: this.page.size,
      realName: this.searchForm.realName,
      yzCode: this.searchForm.yzCode,
      phone: this.searchForm.phone,
      startDate: this.searchForm.dateRange && this.searchForm.dateRange[0] ? this.searchForm.dateRange[0] : '',
      endDate: this.searchForm.dateRange && this.searchForm.dateRange[1] ? this.searchForm.dateRange[1] : ''
    };
    
    const apiConfig = getSignInList(params);
    const response = await this.$post(apiConfig.url, apiConfig.data);
    const result = apiConfig.mapResponse(response);
    
    this.tableData = result.data.list;
    this.page.total = result.data.total;
  } catch (error) {
    console.error(error);
    this.$message.error('获取数据失败');
  } finally {
    this.tableLoading = false;
  }
}
```

### 2. 获取签到规则配置（需要调用两个接口）

```javascript
// 在签到规则配置弹窗中使用
async initData() {
  try {
    const apiConfig = getSignInRules();
    
    // 并发调用两个接口
    const [configResponse, rewardResponse] = await Promise.all([
      this.$get(apiConfig.configUrl),
      this.$get(apiConfig.rewardUrl)
    ]);
    
    const result = apiConfig.mapResponse(configResponse, rewardResponse);
    
    if (result.code === 200) {
      this.form = {
        ruleContent: result.data.ruleContent,
        remindText: result.data.remindText,
        rewardList: result.data.rewardList.length > 0 ? result.data.rewardList : Array.from({ length: 7 }, (_, i) => ({
          days: i + 1,
          points: undefined
        }))
      };
      this.$refs.ruleContent.setContent(result.data.ruleContent);
      this.changeRecords = result.data.changeRecords || [];
      this.total = this.changeRecords.length;
    }
  } catch (error) {
    console.error(error);
    this.$message.error('获取数据失败');
  }
}
```

### 3. 更新签到规则配置（需要调用两个接口）

```javascript
// 保存签到规则
async handleSaveSignRule(data) {
  try {
    const apiConfig = updateSignInRules(data);
    
    // 并发调用两个接口
    const [configResponse, rewardResponse] = await Promise.all([
      this.$post(apiConfig.configUrl, apiConfig.configData),
      this.$post(apiConfig.rewardUrl, apiConfig.rewardData)
    ]);
    
    const result = apiConfig.mapResponse(configResponse, rewardResponse);
    
    this.$message.success(result.message);
    this.$emit('update:visible', false);
  } catch (error) {
    console.error(error);
    this.$message.error(error.message || '保存失败');
  }
}
```

### 4. 获取智米配置列表

```javascript
// 在智米配置弹窗中使用
async fetchData() {
  try {
    this.loading = true;
    const params = {
      page: this.page.current,
      size: this.page.size,
      title: this.searchForm.title,
      enabled: this.searchForm.enabled
    };
    
    const apiConfig = getSmartRiceList(params);
    const response = await this.$post(apiConfig.url, apiConfig.data);
    const result = apiConfig.mapResponse(response);
    
    if (result.code === 200) {
      this.tableData = result.data;
      this.page.total = result.data.length;
    }
  } catch (error) {
    console.error(error);
    this.$message.error('获取数据失败');
  } finally {
    this.loading = false;
  }
}
```

### 5. 新增/更新智米配置

```javascript
// 保存智米配置
async handleSave() {
  try {
    await this.$refs.form.validate();
    
    let apiConfig;
    if (this.formData.id) {
      // 编辑现有数据
      apiConfig = updateSmartRice(this.formData);
    } else {
      // 新增数据
      apiConfig = addSmartRice(this.formData);
    }
    
    const response = await this.$post(apiConfig.url, apiConfig.data);
    const result = apiConfig.mapResponse(response);
    
    this.$message.success(result.message);
    this.drawerVisible = false;
    this.fetchData();
  } catch (error) {
    console.error(error);
    if (error !== false) {
      this.$message.error(error.message || '保存失败');
    }
  }
}
```

## 注意事项

1. **数据映射**: 每个API函数都包含`mapResponse`函数，用于将后端返回的数据格式转换为前端需要的格式。

2. **多接口调用**: 对于需要调用多个接口的函数（如签到规则配置），使用`Promise.all`进行并发调用。

3. **错误处理**: 在`mapResponse`函数中已经包含了基本的错误处理，页面中需要捕获并显示错误信息。

4. **参数验证**: API函数会对传入的参数进行基本的空值处理，但建议在页面中也进行参数验证。

5. **请求方法**: 
   - 获取数据使用`this.$post`或`this.$get`
   - 根据API文档，大部分接口使用POST方法
   - 签到配置详情和奖励配置使用GET方法
